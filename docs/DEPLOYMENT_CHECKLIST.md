# Deployment Checklist

## Pre-Deployment Setup

### Environment Variables

Set the following environment variables for production deployment:

```bash
# MCP Rules Engine Configuration
MCP_RULES_BASE=https://mcp-rules-gateway-prod-1k6gjpoj.uc.gateway.dev
FEATURE_MCP_RULES_ENGINE=true

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=new-texas-laws
MCP_PROJECT=texas-laws-personalinjury
```

### Staging Environment

For staging deployments, use the staging gateway:

```bash
MCP_RULES_BASE=https://mcp-rules-gateway-staging-1k6gjpoj.uc.gateway.dev
```

## Deployment Steps

### 1. Code Preparation
- [ ] All tests passing
- [ ] TypeScript compilation successful
- [ ] Git tagged with `prod-release-YYYYMMDD`

### 2. Environment Configuration
- [ ] Production environment variables set
- [ ] MCP Rules Engine feature flag enabled
- [ ] Database connections verified

### 3. Service Deployment
- [ ] Backend services deployed
- [ ] Frontend application deployed
- [ ] API Gateway endpoints verified

### 4. Post-Deployment Verification
- [ ] Health checks passing
- [ ] MCP Rules Engine responding
- [ ] Tenant creation flow working
- [ ] Error rates < 1%
- [ ] P95 latency < 700ms

## Monitoring

### Key Metrics to Monitor
- API Gateway error rates
- Response latency (P95)
- Circuit breaker status
- Tenant creation success rate

### Alerts Setup
- [ ] Error rate > 2% alert configured
- [ ] Circuit breaker OPEN alert configured
- [ ] High latency alert configured

## Notes

- **Vanity Domain**: Custom domain `rules.ailexlaw.com` is optional; backlog ticket #123
- **Gateway Host**: Currently using Google-provided Gateway hostname
- **Future Enhancement**: Custom domain can be added later without code changes

## Rollback Plan

In case of issues:
1. Revert to previous stable release
2. Switch MCP_RULES_BASE to staging environment
3. Disable FEATURE_MCP_RULES_ENGINE if needed
4. Monitor error rates and restore service

## Support Contacts

- **DevOps**: [Team Contact]
- **Backend**: [Team Contact]  
- **Frontend**: [Team Contact]
