/**
 * Environment Variables Test Suite
 * 
 * Tests that critical environment variables are properly configured
 * for production deployment.
 */

import { describe, it, expect } from '@jest/globals';

describe('Environment Variables', () => {
  describe('MCP Rules Engine Configuration', () => {
    it('should have MCP_RULES_BASE environment variable set', () => {
      const mcpRulesBase = process.env.MCP_RULES_BASE;
      
      expect(mcpRulesBase).toBeDefined();
      expect(mcpRulesBase).not.toBe('');
      expect(typeof mcpRulesBase).toBe('string');
    });

    it('should have MCP_RULES_BASE pointing to a valid Gateway URL', () => {
      const mcpRulesBase = process.env.MCP_RULES_BASE;
      
      if (mcpRulesBase) {
        expect(mcpRulesBase).toMatch(/^https:\/\/mcp-rules-gateway-/);
        expect(mcpRulesBase).toMatch(/\.uc\.gateway\.dev$/);
      }
    });

    it('should have FEATURE_MCP_RULES_ENGINE enabled in CI', () => {
      // Only enforce this in CI environment
      if (process.env.CI === 'true') {
        const featureFlag = process.env.FEATURE_MCP_RULES_ENGINE;
        expect(featureFlag).toBe('true');
      }
    });

    it('should have MCP project configuration', () => {
      const mcpProject = process.env.MCP_PROJECT;
      const tenantProject = process.env.TENANT_PROJECT;
      
      expect(mcpProject).toBeDefined();
      expect(tenantProject).toBeDefined();
      
      if (mcpProject) {
        expect(mcpProject).toBe('texas-laws-personalinjury');
      }
      
      if (tenantProject) {
        expect(tenantProject).toBe('new-texas-laws');
      }
    });
  });

  describe('Critical Environment Variables', () => {
    const criticalVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'SUPABASE_SERVICE_KEY',
      'GOOGLE_CLOUD_PROJECT'
    ];

    criticalVars.forEach(varName => {
      it(`should have ${varName} environment variable set`, () => {
        const value = process.env[varName];
        expect(value).toBeDefined();
        expect(value).not.toBe('');
      });
    });
  });

  describe('Production Environment Validation', () => {
    it('should fail if MCP_RULES_BASE is empty in CI', () => {
      // This test specifically fails if MCP_RULES_BASE is empty in CI
      if (process.env.CI === 'true') {
        const mcpRulesBase = process.env.MCP_RULES_BASE;
        
        if (!mcpRulesBase || mcpRulesBase.trim() === '') {
          throw new Error('MCP_RULES_BASE environment variable is required but empty in CI environment');
        }
        
        expect(mcpRulesBase).toBeTruthy();
      }
    });

    it('should validate MCP_RULES_BASE format in production', () => {
      const mcpRulesBase = process.env.MCP_RULES_BASE;
      const nodeEnv = process.env.NODE_ENV;
      
      if (nodeEnv === 'production' && mcpRulesBase) {
        // In production, should use the production gateway
        expect(mcpRulesBase).toContain('mcp-rules-gateway-prod');
        expect(mcpRulesBase).not.toContain('staging');
      }
    });

    it('should validate staging environment configuration', () => {
      const mcpRulesBase = process.env.MCP_RULES_BASE;
      const nodeEnv = process.env.NODE_ENV;
      
      if (nodeEnv === 'staging' && mcpRulesBase) {
        // In staging, should use the staging gateway
        expect(mcpRulesBase).toContain('mcp-rules-gateway-staging');
      }
    });
  });

  describe('Environment Variable Security', () => {
    it('should not expose sensitive variables in client-side code', () => {
      // Ensure sensitive variables don't start with NEXT_PUBLIC_
      const sensitiveVars = [
        'SUPABASE_SERVICE_KEY',
        'MCP_API_KEY',
        'GOOGLE_CLOUD_PROJECT'
      ];

      sensitiveVars.forEach(varName => {
        expect(varName).not.toMatch(/^NEXT_PUBLIC_/);
      });
    });

    it('should have proper URL format for public variables', () => {
      const publicUrls = [
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_LAWS_API_BASE'
      ];

      publicUrls.forEach(varName => {
        const value = process.env[varName];
        if (value) {
          expect(value).toMatch(/^https?:\/\//);
        }
      });
    });
  });
});
